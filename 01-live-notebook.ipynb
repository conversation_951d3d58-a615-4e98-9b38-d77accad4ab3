{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Vectors and Matrices"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  display: none;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  display: block;\n", "  width: 100%;\n", "  overflow: visible;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".estimator-table summary {\n", "    padding: .5rem;\n", "    font-family: monospace;\n", "    cursor: pointer;\n", "}\n", "\n", ".estimator-table details[open] {\n", "    padding-left: 0.1rem;\n", "    padding-right: 0.1rem;\n", "    padding-bottom: 0.3rem;\n", "}\n", "\n", ".estimator-table .parameters-table {\n", "    margin-left: auto !important;\n", "    margin-right: auto !important;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(odd) {\n", "    background-color: #fff;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(even) {\n", "    background-color: #f6f6f6;\n", "}\n", "\n", ".estimator-table .parameters-table tr:hover {\n", "    background-color: #e0e0e0;\n", "}\n", "\n", ".estimator-table table td {\n", "    border: 1px solid rgba(106, 105, 104, 0.232);\n", "}\n", "\n", ".user-set td {\n", "    color:rgb(255, 94, 0);\n", "    text-align: left;\n", "}\n", "\n", ".user-set td.value pre {\n", "    color:rgb(255, 94, 0) !important;\n", "    background-color: transparent !important;\n", "}\n", "\n", ".default td {\n", "    color: black;\n", "    text-align: left;\n", "}\n", "\n", ".user-set td i,\n", ".default td i {\n", "    color: black;\n", "}\n", "\n", ".copy-paste-icon {\n", "    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48IS0tIUZvbnQgQXdlc29tZSBGcmVlIDYuNy4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlL2ZyZWUgQ29weXJpZ2h0IDIwMjUgRm9udGljb25zLCBJbmMuLS0+PHBhdGggZD0iTTIwOCAwTDMzMi4xIDBjMTIuNyAwIDI0LjkgNS4xIDMzLjkgMTQuMWw2Ny45IDY3LjljOSA5IDE0LjEgMjEuMiAxNC4xIDMzLjlMNDQ4IDMzNmMwIDI2LjUtMjEuNSA0OC00OCA0OGwtMTkyIDBjLTI2LjUgMC00OC0yMS41LTQ4LTQ4bDAtMjg4YzAtMjYuNSAyMS41LTQ4IDQ4LTQ4ek00OCAxMjhsODAgMCAwIDY0LTY0IDAgMCAyNTYgMTkyIDAgMC0zMiA2NCAwIDAgNDhjMCAyNi41LTIxLjUgNDgtNDggNDhMNDggNTEyYy0yNi41IDAtNDgtMjEuNS00OC00OEwwIDE3NmMwLTI2LjUgMjEuNS00OCA0OC00OHoiLz48L3N2Zz4=);\n", "    background-repeat: no-repeat;\n", "    background-size: 14px 14px;\n", "    background-position: 0;\n", "    display: inline-block;\n", "    width: 14px;\n", "    height: 14px;\n", "    cursor: pointer;\n", "}\n", "</style><body><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>MLPClassifier(activation=&#x27;tanh&#x27;, hidden_layer_sizes=[100, 100], max_iter=1000)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>MLPClassifier</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.neural_network.MLPClassifier.html\">?<span>Documentation for MLPClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('hidden_layer_sizes',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">hidden_layer_sizes&nbsp;</td>\n", "            <td class=\"value\">[100, 100]</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('activation',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">activation&nbsp;</td>\n", "            <td class=\"value\">&#x27;tanh&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('solver',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">solver&nbsp;</td>\n", "            <td class=\"value\">&#x27;adam&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('alpha',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">alpha&nbsp;</td>\n", "            <td class=\"value\">0.0001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('batch_size',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">batch_size&nbsp;</td>\n", "            <td class=\"value\">&#x27;auto&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('learning_rate',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">learning_rate&nbsp;</td>\n", "            <td class=\"value\">&#x27;constant&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('learning_rate_init',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">learning_rate_init&nbsp;</td>\n", "            <td class=\"value\">0.001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('power_t',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">power_t&nbsp;</td>\n", "            <td class=\"value\">0.5</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_iter',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_iter&nbsp;</td>\n", "            <td class=\"value\">1000</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('shuffle',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">shuffle&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('random_state',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">random_state&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('tol',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">tol&nbsp;</td>\n", "            <td class=\"value\">0.0001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('warm_start',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">warm_start&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('momentum',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">momentum&nbsp;</td>\n", "            <td class=\"value\">0.9</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('nesterovs_momentum',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">nesterovs_momentum&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('early_stopping',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">early_stopping&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('validation_fraction',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">validation_fraction&nbsp;</td>\n", "            <td class=\"value\">0.1</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('beta_1',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">beta_1&nbsp;</td>\n", "            <td class=\"value\">0.9</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('beta_2',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">beta_2&nbsp;</td>\n", "            <td class=\"value\">0.999</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('epsilon',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">epsilon&nbsp;</td>\n", "            <td class=\"value\">1e-08</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_iter_no_change',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_iter_no_change&nbsp;</td>\n", "            <td class=\"value\">10</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_fun',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_fun&nbsp;</td>\n", "            <td class=\"value\">15000</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div><script>function copyToClipboard(text, element) {\n", "    // Get the parameter prefix from the closest toggleable content\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${text}` : text;\n", "\n", "    const originalStyle = element.style;\n", "    const computedStyle = window.getComputedStyle(element);\n", "    const originalWidth = computedStyle.width;\n", "    const originalHTML = element.innerHTML.replace('Copied!', '');\n", "\n", "    navigator.clipboard.writeText(fullParamName)\n", "        .then(() => {\n", "            element.style.width = originalWidth;\n", "            element.style.color = 'green';\n", "            element.innerHTML = \"Copied!\";\n", "\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        })\n", "        .catch(err => {\n", "            console.error('Failed to copy:', err);\n", "            element.style.color = 'red';\n", "            element.innerHTML = \"Failed!\";\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        });\n", "    return false;\n", "}\n", "\n", "document.querySelectorAll('.fa-regular.fa-copy').forEach(function(element) {\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const paramName = element.parentElement.nextElementSibling.textContent.trim();\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${paramName}` : paramName;\n", "\n", "    element.setAttribute('title', fullParamName);\n", "});\n", "</script></body>"], "text/plain": ["MLPClassifier(activation='tanh', hidden_layer_sizes=[100, 100], max_iter=1000)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.datasets import load_iris\n", "from sklearn.neural_network import MLPClassifier\n", "\n", "iris_data = load_iris()\n", "X, y = iris_data[\"data\"], iris_data[\"target\"]\n", "\n", "model = MLPClassifier(\n", "    hidden_layer_sizes=[100, 100],\n", "    activation=\"tanh\",\n", "    max_iter=1000\n", ")\n", "model.fit(X, y)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "       1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "       1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,\n", "       2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,\n", "       2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[5.1, 3.5, 1.4, 0.2],\n", "       [4.9, 3. , 1.4, 0.2],\n", "       [4.7, 3.2, 1.3, 0.2],\n", "       [4.6, 3.1, 1.5, 0.2],\n", "       [5. , 3.6, 1.4, 0.2],\n", "       [5.4, 3.9, 1.7, 0.4],\n", "       [4.6, 3.4, 1.4, 0.3],\n", "       [5. , 3.4, 1.5, 0.2],\n", "       [4.4, 2.9, 1.4, 0.2],\n", "       [4.9, 3.1, 1.5, 0.1]])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["X[:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vectors"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["x = (1, 2, 3)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["tuple"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["type(x)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["y = (4, 5, 6)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 2, 3, 4, 5, 6)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["x + y"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5, 6]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["x = [1, 2, 3]\n", "y = [4, 5, 6]\n", "\n", "x + y"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 1, 2, 3]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["2 * x"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3, 1, 2, 3]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["5 * x"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    def __init__(self, coords):\n", "        self.coords = coords"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3]\n"]}], "source": ["x.coords"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["<__main__.Vector at 0x7d84ce31e8a0>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    def __init__(self, coords):\n", "        self.coords = coords\n", "\n", "    def __repr__(self):\n", "        return str(self.coords)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'Vector' object is not subscriptable", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[24]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mx\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m]\u001b[49m\n", "\u001b[31mTypeError\u001b[39m: 'Vector' object is not subscriptable"]}], "source": ["x[1]"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    def __init__(self, coords):\n", "        self.coords = coords\n", "\n", "    def __repr__(self):\n", "        return str(self.coords)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.coords[idx]\n", "\n", "    def __setitem__(self, idx, value):\n", "        self.coords[idx] = value"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["x[0]"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["x[0] = 42"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["x[0] = \"this is a string\""]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["['this is a string', 2, 3]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "unsupported operand type(s) for +: 'Vector' and 'Vector'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[39]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m x = Vector([\u001b[32m1\u001b[39m, \u001b[32m2\u001b[39m, \u001b[32m3\u001b[39m])\n\u001b[32m      2\u001b[39m y = Vector([\u001b[32m4\u001b[39m, \u001b[32m5\u001b[39m, \u001b[32m6\u001b[39m])\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m \u001b[43mx\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\n", "\u001b[31mTypeError\u001b[39m: unsupported operand type(s) for +: 'Vector' and 'Vector'"]}], "source": ["x = Vector([1, 2, 3])\n", "y = Vector([4, 5, 6])\n", "\n", "x + y"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5, 6]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["l1 = [1, 2, 3]\n", "l2 = [4, 5, 6]\n", "\n", "l1.__add__(l2) # equivalent to l1 + l2"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    def __init__(self, coords):\n", "        self.coords = coords\n", "\n", "    def __repr__(self):\n", "        return str(self.coords)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.coords[idx]\n", "\n", "    def __setitem__(self, idx, value):\n", "        self.coords[idx] = value\n", "\n", "    def __add__(self, other):\n", "        return Vector([x + y for x, y in zip(self.coords, other.coords)])"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["[5, 7, 9]"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["x = Vector([1, 2, 3])\n", "y = Vector([4, 5, 6])\n", "\n", "x + y"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    def __init__(self, coords):\n", "        self.coords = coords\n", "\n", "    def __repr__(self):\n", "        return str(self.coords)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.coords[idx]\n", "\n", "    def __setitem__(self, idx, value):\n", "        self.coords[idx] = value\n", "\n", "    def __add__(self, other):\n", "        return Vector([x + y for x, y in zip(self.coords, other.coords)])\n", "    \n", "    def __mul__(self, other):\n", "        return Vector([other * x for x in self.coords])"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0, 4.0, 6.0]"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["x * 2.0 # x.__mul__(2.0)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0, 4.0, 6.0]"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["x.__mul__(2.0)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "unsupported operand type(s) for *: 'float' and 'Vector'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36m<PERSON>ell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[51]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[32;43m2.0\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\n", "\u001b[31mTypeError\u001b[39m: unsupported operand type(s) for *: 'float' and 'Vector'"]}], "source": ["2.0 * x # 2.0.__mul__(x)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    def __init__(self, coords):\n", "        self.coords = coords\n", "\n", "    def __repr__(self):\n", "        return str(self.coords)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.coords[idx]\n", "\n", "    def __setitem__(self, idx, value):\n", "        self.coords[idx] = value\n", "\n", "    def __add__(self, other):\n", "        return Vector([x + y for x, y in zip(self.coords, other.coords)])\n", "    \n", "    def __mul__(self, other):\n", "        return Vector([other * x for x in self.coords])\n", "    \n", "    def __rmul__(self, other):\n", "        return self * other"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0, 4.0, 6.0]"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["x * 2.0"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0, 4.0, 6.0]"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["2.0 * x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vectors in NumPy"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["x = np.array([1, 2, 3])\n", "y = np.array([4, 5, 6])"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5, 7, 9])"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["x + y"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2., 4., 6.])"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["2.0 * x"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(1)"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["x[0]"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "invalid literal for int() with base 10: 'this is a string'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[62]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mx\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m = \u001b[33m\"\u001b[39m\u001b[33mthis is a string\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[31mValueError\u001b[39m: invalid literal for int() with base 10: 'this is a string'"]}], "source": ["x[0] = \"this is a string\""]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 4, 10, 18])"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["x * y"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(2.718281828459045)"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["np.exp(1)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 2.71828183,  7.3890561 , 20.08553692])"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["np.exp(x)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["def dot(xs, ys):\n", "    return sum([x * y for x, y in zip(xs, ys)])"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["-0.3"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["dot([1, 0.5], [0.2, -1])"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(32)"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["np.dot(x, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Matrices"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["from sklearn.datasets import fetch_california_housing\n", "\n", "housing_data = fetch_california_housing()\n", "X, y = housing_data[\"data\"], housing_data[\"target\"]"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 8.32520000e+00,  4.10000000e+01,  6.98412698e+00,\n", "         1.02380952e+00,  3.22000000e+02,  2.55555556e+00,\n", "         3.78800000e+01, -1.22230000e+02],\n", "       [ 8.30140000e+00,  2.10000000e+01,  6.23813708e+00,\n", "         9.71880492e-01,  2.40100000e+03,  2.10984183e+00,\n", "         3.78600000e+01, -1.22220000e+02],\n", "       [ 7.25740000e+00,  5.20000000e+01,  8.28813559e+00,\n", "         1.07344633e+00,  4.96000000e+02,  2.80225989e+00,\n", "         3.78500000e+01, -1.22240000e+02],\n", "       [ 5.64310000e+00,  5.20000000e+01,  5.81735160e+00,\n", "         1.07305936e+00,  5.58000000e+02,  2.54794521e+00,\n", "         3.78500000e+01, -1.22250000e+02],\n", "       [ 3.84620000e+00,  5.20000000e+01,  6.28185328e+00,\n", "         1.08108108e+00,  5.65000000e+02,  2.18146718e+00,\n", "         3.78500000e+01, -1.22250000e+02]])"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["X[:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "neural-networks-from-scratch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}