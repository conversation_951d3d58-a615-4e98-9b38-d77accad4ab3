{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Vectors and matrices\n", "\n", "Hey there!\n", "\n", "Welcome to the first lesson of our *Neural Networks from Scratch* course, where we'll build a fully functional tensor library (with automatic differentiation and all) in NumPy, mastering the inner workings of neural networks meanwhile.\n", "\n", "Before we dive deep into the meaty parts and start building computational graphs from left and right, we'll take a brief look at the building blocks of machine learning: *vectors* and *matrices*.\n", "\n", "To see what I'm talking about, let's take a look at machine learning from a high level. Training a model is simpler than you think: it can be done in just six lines, including loading the data."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from sklearn.datasets import load_iris\n", "from sklearn.neural_network import MLPClassifier"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["iris_data = load_iris()\n", "X, y = iris_data[\"data\"], iris_data[\"target\"]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  display: none;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  display: block;\n", "  width: 100%;\n", "  overflow: visible;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".estimator-table summary {\n", "    padding: .5rem;\n", "    font-family: monospace;\n", "    cursor: pointer;\n", "}\n", "\n", ".estimator-table details[open] {\n", "    padding-left: 0.1rem;\n", "    padding-right: 0.1rem;\n", "    padding-bottom: 0.3rem;\n", "}\n", "\n", ".estimator-table .parameters-table {\n", "    margin-left: auto !important;\n", "    margin-right: auto !important;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(odd) {\n", "    background-color: #fff;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(even) {\n", "    background-color: #f6f6f6;\n", "}\n", "\n", ".estimator-table .parameters-table tr:hover {\n", "    background-color: #e0e0e0;\n", "}\n", "\n", ".estimator-table table td {\n", "    border: 1px solid rgba(106, 105, 104, 0.232);\n", "}\n", "\n", ".user-set td {\n", "    color:rgb(255, 94, 0);\n", "    text-align: left;\n", "}\n", "\n", ".user-set td.value pre {\n", "    color:rgb(255, 94, 0) !important;\n", "    background-color: transparent !important;\n", "}\n", "\n", ".default td {\n", "    color: black;\n", "    text-align: left;\n", "}\n", "\n", ".user-set td i,\n", ".default td i {\n", "    color: black;\n", "}\n", "\n", ".copy-paste-icon {\n", "    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48IS0tIUZvbnQgQXdlc29tZSBGcmVlIDYuNy4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlL2ZyZWUgQ29weXJpZ2h0IDIwMjUgRm9udGljb25zLCBJbmMuLS0+PHBhdGggZD0iTTIwOCAwTDMzMi4xIDBjMTIuNyAwIDI0LjkgNS4xIDMzLjkgMTQuMWw2Ny45IDY3LjljOSA5IDE0LjEgMjEuMiAxNC4xIDMzLjlMNDQ4IDMzNmMwIDI2LjUtMjEuNSA0OC00OCA0OGwtMTkyIDBjLTI2LjUgMC00OC0yMS41LTQ4LTQ4bDAtMjg4YzAtMjYuNSAyMS41LTQ4IDQ4LTQ4ek00OCAxMjhsODAgMCAwIDY0LTY0IDAgMCAyNTYgMTkyIDAgMC0zMiA2NCAwIDAgNDhjMCAyNi41LTIxLjUgNDgtNDggNDhMNDggNTEyYy0yNi41IDAtNDgtMjEuNS00OC00OEwwIDE3NmMwLTI2LjUgMjEuNS00OCA0OC00OHoiLz48L3N2Zz4=);\n", "    background-repeat: no-repeat;\n", "    background-size: 14px 14px;\n", "    background-position: 0;\n", "    display: inline-block;\n", "    width: 14px;\n", "    height: 14px;\n", "    cursor: pointer;\n", "}\n", "</style><body><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>MLPClassifier(activation=&#x27;tanh&#x27;, hidden_layer_sizes=[100, 100], max_iter=1000)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>MLPClassifier</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.neural_network.MLPClassifier.html\">?<span>Documentation for MLPClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('hidden_layer_sizes',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">hidden_layer_sizes&nbsp;</td>\n", "            <td class=\"value\">[100, 100]</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('activation',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">activation&nbsp;</td>\n", "            <td class=\"value\">&#x27;tanh&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('solver',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">solver&nbsp;</td>\n", "            <td class=\"value\">&#x27;adam&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('alpha',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">alpha&nbsp;</td>\n", "            <td class=\"value\">0.0001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('batch_size',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">batch_size&nbsp;</td>\n", "            <td class=\"value\">&#x27;auto&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('learning_rate',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">learning_rate&nbsp;</td>\n", "            <td class=\"value\">&#x27;constant&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('learning_rate_init',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">learning_rate_init&nbsp;</td>\n", "            <td class=\"value\">0.001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('power_t',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">power_t&nbsp;</td>\n", "            <td class=\"value\">0.5</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_iter',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_iter&nbsp;</td>\n", "            <td class=\"value\">1000</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('shuffle',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">shuffle&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('random_state',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">random_state&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('tol',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">tol&nbsp;</td>\n", "            <td class=\"value\">0.0001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('warm_start',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">warm_start&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('momentum',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">momentum&nbsp;</td>\n", "            <td class=\"value\">0.9</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('nesterovs_momentum',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">nesterovs_momentum&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('early_stopping',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">early_stopping&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('validation_fraction',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">validation_fraction&nbsp;</td>\n", "            <td class=\"value\">0.1</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('beta_1',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">beta_1&nbsp;</td>\n", "            <td class=\"value\">0.9</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('beta_2',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">beta_2&nbsp;</td>\n", "            <td class=\"value\">0.999</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('epsilon',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">epsilon&nbsp;</td>\n", "            <td class=\"value\">1e-08</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_iter_no_change',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_iter_no_change&nbsp;</td>\n", "            <td class=\"value\">10</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_fun',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_fun&nbsp;</td>\n", "            <td class=\"value\">15000</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div><script>function copyToClipboard(text, element) {\n", "    // Get the parameter prefix from the closest toggleable content\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${text}` : text;\n", "\n", "    const originalStyle = element.style;\n", "    const computedStyle = window.getComputedStyle(element);\n", "    const originalWidth = computedStyle.width;\n", "    const originalHTML = element.innerHTML.replace('Copied!', '');\n", "\n", "    navigator.clipboard.writeText(fullParamName)\n", "        .then(() => {\n", "            element.style.width = originalWidth;\n", "            element.style.color = 'green';\n", "            element.innerHTML = \"Copied!\";\n", "\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        })\n", "        .catch(err => {\n", "            console.error('Failed to copy:', err);\n", "            element.style.color = 'red';\n", "            element.innerHTML = \"Failed!\";\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        });\n", "    return false;\n", "}\n", "\n", "document.querySelectorAll('.fa-regular.fa-copy').forEach(function(element) {\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const paramName = element.parentElement.nextElementSibling.textContent.trim();\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${paramName}` : paramName;\n", "\n", "    element.setAttribute('title', fullParamName);\n", "});\n", "</script></body>"], "text/plain": ["MLPClassifier(activation='tanh', hidden_layer_sizes=[100, 100], max_iter=1000)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["model = MLPClassifier(\n", "    hidden_layer_sizes=[100, 100],\n", "    activation=\"tanh\",\n", "    max_iter=1000\n", ")\n", "model.fit(X, y)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.98"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["model.score(X, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["No matter where we come from, this is fascinating. Modern frameworks (such as scikit-learn and PyTorch) hide a ton of complexity from the user, allowing us to build and experiment faster than ever. However, this comes with a price. Pre-built solutions can take you so far, but to master machine learning, you must understand how the algorithms work.\n", "\n", "This course is about what's behind the hood. We'll take these six lines, deconstruct them, and rebuild everything from scratch.\n", "\n", "We'll start with the data, that is, the `X` and `y` objects. Let's take a look!"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[5.1, 3.5, 1.4, 0.2],\n", "       [4.9, 3. , 1.4, 0.2],\n", "       [4.7, 3.2, 1.3, 0.2],\n", "       [4.6, 3.1, 1.5, 0.2],\n", "       [5. , 3.6, 1.4, 0.2],\n", "       [5.4, 3.9, 1.7, 0.4],\n", "       [4.6, 3.4, 1.4, 0.3],\n", "       [5. , 3.4, 1.5, 0.2],\n", "       [4.4, 2.9, 1.4, 0.2],\n", "       [4.9, 3.1, 1.5, 0.1]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["X[:10]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "       1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n", "       1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,\n", "       2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,\n", "       2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["These are known as *vectors* and *matrices*. Intuitively, we can think about vectors as *tuples of numbers*, like $ (-1.2, 3,5, \\pi) $, and matrices as *tables of scalars*, like\n", "\n", "$$\n", "\\begin{bmatrix}\n", "0 & 1 & 2 \\\\\n", "2 & 3 & 4 \\\\\n", "4 & 5 & 6 \\\\\n", "6 & 7 & 8 \\\\\n", "\\end{bmatrix}.\n", "$$\n", "\n", "What truly gives vectors and matrices their power is not just their structure, but their *operations*. We can add and scale vectors, as well as add, scale, and multiply matrices.\n", "\n", "Let's start with vectors first."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vectors\n", "\n", "In mathematical notation, vectors are (usually) defined by bold letters, such as $ \\mathbf{x} = (x_1, \\dots, x_n) $, and writing $ \\mathbf{x} \\in \\mathbb{R}^n $ means that $ \\mathbf{x} $ is a vector of $ n $ real numbers. Mathematically speaking, you can imagine a vector as an arrow starting from the origin, pointing to the coordinate $ (x_1, \\dots, x_n) $:\n", "\n", "![Vectors](figures/01-vectors-and-matrices/01.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are two fundamental operations for vectors: addition and scalar multiplication, defined by\n", "\n", "$$\n", "(x_1, \\dots, x_n) + (y_1, \\dots, y_n) = (x_1 + y_1, \\dots, x_n + y_n),\n", "$$\n", "\n", "and\n", "\n", "$$\n", "c (x_1, \\dots, x_n) = (c x_1, \\dots, c x_n).\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can (probably) recall from your high school math classes that in the Euclidean plane, we illustrated addition with the parallelogram rule:\n", "\n", "![The parallelogram rule](figures/01-vectors-and-matrices/02.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["On the other hand, you can imagine scalar multiplication as a scaling. If $ c > 0 $, then $ c \\mathbf{x} $ stretches out $ \\mathbb{x} $, but if $ 0 < c < 1 $, $ c \\mathbf{x} $ is squeezed.\n", "\n", "![The parallelogram rule](figures/01-vectors-and-matrices/03.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Vectors in Python\n", "\n", "How do we represent vectors in Python? The first idea is to use the built-in `tuple` structure."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["x = (1, 2, 3)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["tuple"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["type(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here's the issue: we can't perform vector operations with tuples. Take a guess what happens when we try to add two `tuple` objects."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 2, 3, 4, 5, 6)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["x = (1, 2, 3)\n", "y = (4, 5, 6)\n", "\n", "x + y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The result is the *concatenation* of `x` and `y`, which is not what we expected. The same thing happens with Python's built-in `list` structure."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5, 6]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["x = [1, 2, 3]\n", "y = [4, 5, 6]\n", "\n", "x + y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["No spoilers here: there is an existing solution, but first, let's build our own. It's instructive and teaches us the fundamentals of object-oriented programming in Python, a feature that we'll use extensively when building our neural network library.\n", "\n", "So, let's create a custom `Vector` class! In Python, this is done with the `class` keyword."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can start adding *methods*, which you can think of as functions attached to the class. The first method we'll add is the `__init__`, which runs when an object from the class is instantiated. Let's see this in action; it'll be simpler to understand. I promise.\n", "\n", "Our `Vector`'s `__init__` will take a container `coords` with the — surprise! — coordinates, and stores them internally."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    def __init__(self, coords):\n", "        self.coords = coords"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are several oddities here, so let's unpack. The first argument of `__init__` is always `self`, which describes the concrete object that is being initialized. In our example, the coordinates are attached as an *attribute* named `coords`. (The attribute name does not have to match the argument name; it's just easier to understand this way.)\n", "\n", "Let's try this."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["x = Vector(coords=[1, 2, 3])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["<__main__.Vector at 0x7fdd5ba8d3d0>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["x.coords"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As you can see, the coordinates can be accessed by `v.coords`. (In general, the attribute of any object can be accessed with the syntax `object_name.attr_name`.) However, by printing out the object `v`, we get an obscure `<__main__.Vector at 0x70ad76672db0>`, or something like this. (It'll be different on your computer.) This describes\n", "\n", "* the class,\n", "* and the memory address\n", "\n", "of the particular object `v`. To make this more user-friendly, we can add a string representation to the `Vector` class by implementing the method `__repr__`."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["class Vector(Vector):\n", "    def __repr__(self):\n", "        return str(self.coords)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["(Instead of re-implementing every method, I subclass the previous version to avoid code repetition. Writing\n", "\n", "```<PERSON>\n", "class Vector(Vector):\n", "    def __repr__(self):\n", "        return str(self.coords)\n", "```\n", "\n", "is essentially equivalent to\n", "\n", "```<PERSON>\n", "class Vector:\n", "    def __init__(self, coords):\n", "        self.coords = coords\n", "\n", "    def __repr__(self):\n", "        return str(self.coords)\n", "```\n", "\n", "only much shorter as the number of methods grows.)\n", "\n", "Let's try this."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["x = Vector(coords=[1, 2, 3])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Much better.\n", "\n", "By the way, method names are not necessarily enclosed between double underscore (a.k.a. *dunder*) characters. Methods pre- and suffixed with dunders are called *magic methods*, and they each set the behavior of Python objects with respect to operations, indexing, etc. They are key to implementing classes that smoothly work together with Python features.\n", "\n", "Let's talk about the indexing. Currently, accessing and setting the coordinates requires accessing the `coords` attribute, which is not a good practice, as it is internal to the object. These can be implemented by the `__getitem__` and `__setitem__` methods."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["class Vector(Vector):   \n", "    def __getitem__(self, idx):\n", "        return self.coords[idx]\n", "    \n", "    def __setitem__(self, idx, value):\n", "        self.coords[idx] = value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's test."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["x[0]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["x[0] = 42"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[42, 2, 3]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Yay! Now, it's finally time to do the operations. Addition is easier, so let's do that first. In Python, the expression `a + b` is equivalent to calling the `__add__` method of `a` with `b`:"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["[1, 2].__add__([3, 4])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So, it's time to implement `__add__` for the `Vector` class. It's simpler than you think! `__add__` requires two arguments: the usual `self`, and the other object conventionally named `other`."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["class Vector(Vector):\n", "    def __add__(self, other):\n", "        return Vector(coords=[x + y for x, y in zip(self.coords, other.coords)])"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])\n", "y = Vector([4, 5, 6])"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["[5, 7, 9]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["x + y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It works! Now, the scalar multiplication. This is controlled by the `__mul__` magic method, once again taking `self` and `other`."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["class Vector(Vector):\n", "    def __mul__(self, other):\n", "        return Vector([other * x for x in self.coords])"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0, 4.0, 6.0]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["x * 2.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What's the issue? Check out the expression `2.0 * x`."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "unsupported operand type(s) for *: 'float' and 'Vector'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[31]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[32;43m2.0\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\n", "\u001b[31mTypeError\u001b[39m: unsupported operand type(s) for *: 'float' and 'Vector'"]}], "source": ["2.0 * x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The problem is that `2.0 * x` is equivalent to `2.0.__mul__(x)`, but the `__mul__` of floats does not support `Vector` arguments. (Which is not a huge surprise, as we have just implemented the `Vector` class.)\n", "\n", "To solve this, we need the `__rmul__` method, implementing multiplication from right. Thus, here's roughly what happens inside Python when we write `x.__mul__(y)`.\n", "\n", "1. Does `x.__mul__(y)` work? If yes, we are happy.\n", "2. If not, let's try `y.__rmul__(x)`. Does it work? If yes, we are happy.\n", "3. If `y.__rmul__(x)` fails as well, we are not happy, and throw an exception as a result."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["class Vector(Vector):\n", "    def __rmul__(self, other):\n", "        return Vector(coords=[other * x for x in self.coords])"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0, 4.0, 6.0]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["x * 2.0"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0, 4.0, 6.0]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["2.0 * x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Yay! However, there's still an issue. A deep, fundamental issue.\n", "\n", "Unfortunately, Python's built-in data structures are slow. Flexible, user-friendly, but slow. The solution is given by NumPy, which we'll check out now."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Vectors in NumPy\n", "\n", "Now that we have built our very own custom `Vector` class, it's time to discard it and use the real deal: NumPy, which implements the classic C array in Python. Without going (too much) into the details, NumPy arrays are fast because they are \n", "\n", "1. written in C,\n", "2. homogeneous, that is, only objects with matching types can be stored in a single array,\n", "3. and objects are stored next to each other in memory, making the random access a fast operation.\n", "\n", "Let's fire it up quickly and play around a bit."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["x = np.array([1, 2, 3])\n", "y = np.array([4, 5, 6])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 2, 3])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5, 7, 9])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["x + y"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2., 4., 6.])"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["2.0 * x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Yay, one more time!\n", "\n", "As usual, <PERSON><PERSON><PERSON><PERSON> does not always work as we expect. If you are coming from a more mathematical education (like I am), the following result might surprise you:"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 4, 10, 18])"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["x * y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here, the expression `x * y` performs the *elementwise product* operation. It turns out that this is somewhat of a modus operandi for NumPy, and trust me on this, it's one of the features that makes it ideal for our educational purposes. For instance, NumPy also applies functions elementwise, which we'll use extensively. Check it out:"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 2.71828183,  7.3890561 , 20.08553692])"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["np.exp(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The dot product\n", "\n", "Regarding vectors, one of the most essential operations is the so-called *dot product*, defined by\n", "\n", "$$\n", "\\mathbf{x} \\cdot \\mathbf{y} = \\sum_{i=1}^{n} x_i y_i.\n", "$$\n", "\n", "In English, the dot product takes the product of the corresponding coordinates, then multiplies them together. To motivate this, consider the famous California housing dataset, which consists of eight features per sample and a single target variable; that is, each data point is an eight-dimensional vector, from which we are supposed to predict a real number.\n", "\n", "The simplest predictive model is the famous *multivariate linear regression*, which can be written in terms of the dot product:\n", "\n", "$$\n", "\\begin{align*}\n", "L(\\mathbf{x}) &= \\sum_{i=1}^{n} w_i x_i \\\\\n", "&= \\langle \\mathbf{w}, \\mathbf{x} \\rangle.\n", "\\end{align*}\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The dot product is straightforward to implement in Python. Here's a vanilla version:"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["def dot(x, y):\n", "    return sum([xi * yi for xi, yi in zip(x, y)])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It's also implemented in NumPy, which is what we'll use."]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["x = np.array([1, 2, 3])\n", "y = np.array([4, 5, 6])"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(32)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["np.dot(x, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With the dot product, we are familiar with the essentials of vectors. It's time to move on to matrices!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Matrices\n", "\n", "Let's go back to the California housing dataset we talked about earlier. We can import it right away from scikit-learn."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["from sklearn.datasets import fetch_california_housing\n", "\n", "housing_data = fetch_california_housing()\n", "X, y = housing_data[\"data\"], housing_data[\"target\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's inspect `X`. (At least, the first couple of samples.)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 8.32520000e+00,  4.10000000e+01,  6.98412698e+00,\n", "         1.02380952e+00,  3.22000000e+02,  2.55555556e+00,\n", "         3.78800000e+01, -1.22230000e+02],\n", "       [ 8.30140000e+00,  2.10000000e+01,  6.23813708e+00,\n", "         9.71880492e-01,  2.40100000e+03,  2.10984183e+00,\n", "         3.78600000e+01, -1.22220000e+02],\n", "       [ 7.25740000e+00,  5.20000000e+01,  8.28813559e+00,\n", "         1.07344633e+00,  4.96000000e+02,  2.80225989e+00,\n", "         3.78500000e+01, -1.22240000e+02],\n", "       [ 5.64310000e+00,  5.20000000e+01,  5.81735160e+00,\n", "         1.07305936e+00,  5.58000000e+02,  2.54794521e+00,\n", "         3.78500000e+01, -1.22250000e+02],\n", "       [ 3.84620000e+00,  5.20000000e+01,  6.28185328e+00,\n", "         1.08108108e+00,  5.65000000e+02,  2.18146718e+00,\n", "         3.78500000e+01, -1.22250000e+02]])"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["X[:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is what's called a *matrix*. In mathematical notation, we write matrices as\n", "\n", "$$\n", "X = \\begin{bmatrix}\n", "x_{1, 1} & x_{1, 2} & \\dots & x_{1, m} \\\\\n", "x_{2, 1} & x_{2, 2} & \\dots & x_{2, m} \\\\\n", "\\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "x_{n, 1} & x_{n, 2} & \\dots & x_{n, m} \\\\\n", "\\end{bmatrix} \\in \\mathbb{R}^{n \\times m},\n", "$$\n", "\n", "meaning that \"$ X $ is an $ n \\times m $ matrix.\" (Matrices are usually denoted by capital Latin letters.) The element $ x_{i, j} $ is the $ j $-th element of the $ i $-th row, or equivalently, the $ i $-th element of the $ j $-th column."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Remember the multivariate linear regression model we talked about when discussing the dot product? Let's push that example a bit more.\n", "\n", "Recall that we have the model\n", "\n", "$$\n", "L(\\mathbf{x}) = \\sum_{i=1}^{m} x_i w_i = \\langle \\mathbf{x}, \\mathbf{w} \\rangle.\n", "$$\n", "\n", "What if we want to compute the predictions for more than one data point? Bear with me. If we structure the parameters of the model into a *column vector* $ \\mathbf{w} \\in \\mathbb{R}^{m \\times 1} $, that is, an $ n \\times 1 $ matrix, we could write something like\n", "\n", "$$\n", "\\begin{bmatrix}\n", "x_{1, 1} & x_{1, 2} & \\dots & x_{1, m} \\\\\n", "x_{2, 1} & x_{2, 2} & \\dots & x_{2, m} \\\\\n", "\\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "x_{n, 1} & x_{n, 2} & \\dots & x_{n, m} \\\\\n", "\\end{bmatrix} \\begin{bmatrix}\n", "w_1 \\\\\n", "w_2 \\\\\n", "\\vdots \\\\\n", "w_m\n", "\\end{bmatrix} = \\begin{bmatrix}\n", "\\langle \\mathbf{x}_1, \\mathbf{w} \\rangle \\\\\n", "\\langle \\mathbf{x}_2, \\mathbf{w} \\rangle \\\\\n", "\\vdots \\\\\n", "\\langle \\mathbf{x}_n, \\mathbf{w} \\rangle \\\\\n", "\\end{bmatrix},\n", "$$\n", "\n", "where $ \\mathbf{x}_i \\in \\mathbb{R}^{m (\\times 1)} $ denotes the $ i $-th *row* of $ X $.\n", "\n", "Congratulations! You've just discovered the *matrix-vector product*."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can take this even further: suppose that instead of one multivariate linear regression model, we have $ m $ models, each governed by the parameters $ \\mathbf{w}^{(i)} \\in \\mathbb{R}^{m \\times 1} $. Now, we can compute the predictions of all models on all samples by\n", "\n", "$$\n", "\\begin{bmatrix}\n", "x_{1, 1} & x_{1, 2} & \\dots & x_{1, m} \\\\\n", "x_{2, 1} & x_{2, 2} & \\dots & x_{2, m} \\\\\n", "\\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "x_{n, 1} & x_{n, 2} & \\dots & x_{n, m} \\\\\n", "\\end{bmatrix}\n", "\\begin{bmatrix}\n", "w_{1, 1} & w_{1, 2} & \\dots & w_{1, k} \\\\\n", "w_{2, 1} & w_{2, 2} & \\dots & w_{2, k} \\\\\n", "\\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "w_{m, 1} & w_{m, 2} & \\dots & w_{m, k} \\\\\n", "\\end{bmatrix} =\n", "\\begin{bmatrix}\n", "\\langle \\mathbf{x}_1, \\mathbf{w}^{(1)} \\rangle & \\langle \\mathbf{x}_1, \\mathbf{w}^{(2)} \\rangle & \\dots & \\langle \\mathbf{x}_1, \\mathbf{w}^{(k)} \\rangle \\\\\n", "\\langle \\mathbf{x}_2, \\mathbf{w}^{(1)} \\rangle & \\langle \\mathbf{x}_2, \\mathbf{w}^{(2)} \\rangle & \\dots & \\langle \\mathbf{x}_2, \\mathbf{w}^{(k)} \\rangle \\\\\n", "\\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "\\langle \\mathbf{x}_n, \\mathbf{w}^{(1)} \\rangle & \\langle \\mathbf{x}_n, \\mathbf{w}^{(2)} \\rangle & \\dots & \\langle \\mathbf{x}_n, \\mathbf{w}^{(k)} \\rangle \\\\\n", "\\end{bmatrix}.\n", "$$\n", "\n", "Congratulations! You've just discovered the *matrix product*. (The single most important operation in machine learning.)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Matrices in Python\n", "\n", "You know the deal by now. First, we'll build our custom `Matrix` class, then we'll learn NumPy. I insist. If you truly want to understand matrix multiplication, you *have to* implement one from scratch. Let's start.\n", "\n", "We'll represent matrices as *lists of lists*, where each list will represent a row."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["class Matrix:\n", "    def __init__(self, rows):\n", "        self.rows = rows\n", "        self.shape = (len(self.rows), len(self.rows[0]))\n", "\n", "    def __repr__(self):\n", "        if not self.rows:\n", "            return \"[]\"\n", "        if not self.rows[0]:\n", "            return \"[[]]\"\n", "\n", "        col_widths = [max(len(str(item)) for item in col)\n", "                      for col in zip(*self.rows)]\n", "\n", "        row_strings = [\n", "            f\"[{'  '.join(str(item).rjust(col_widths[i])\n", "                for i, item in enumerate(row))}]\"\n", "            for row in self.rows\n", "        ]\n", "\n", "        return '\\n'.join(row_strings)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I have added a pretty string representation, which, I won't lie, I made with ChatGPT. Don't even spend time on looking at it, it's not relevant for our purposes.\n", "\n", "For convenience, the shape of the matrix is also recorded upon initialization. (We are also assuming that the \"rows\" passed upon initialization consist of lists of the same length.)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["X = Matrix([[1, 2, 3],\n", "            [4, 5, 6],\n", "            [7, 8, 9],\n", "            [10, 11, 12]])"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ 1   2   3]\n", "[ 4   5   6]\n", "[ 7   8   9]\n", "[10  11  12]"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["X"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looks good. Let's add a simple indexing via `__getitem__` and `__setitem__`."]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["class Matrix(Matrix):\n", "    def __getitem__(self, idx):\n", "        i, j = idx[0], idx[1]\n", "        return self.rows[i][j]\n", "    \n", "    def __setitem__(self, idx, value):\n", "        i, j = idx[0], idx[1]\n", "        self.rows[i][j] = value"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["X = Matrix([[1, 2, 3],\n", "            [4, 5, 6],\n", "            [7, 8, 9],\n", "            [10, 11, 12]])"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["X[0, 0]"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["X[0, 0] = 42"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["[42   2   3]\n", "[ 4   5   6]\n", "[ 7   8   9]\n", "[10  11  12]"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["X"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looks good as well. Now, the matrix addition and scalar multiplication. Matrices are also added elementwise, so it should be a simple exercise for us by now."]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["class Matrix(Matrix):\n", "    def __add__(self, other):\n", "        return Matrix([\n", "            [x + y for x, y in zip(xs, ys)]\n", "            for xs, ys in zip(self.rows, other.rows) \n", "        ])\n", "    \n", "    def __mul__(self, other):\n", "        return Matrix([\n", "            [other * x for x in xs]\n", "            for xs in self.rows\n", "        ])\n", "    \n", "    def __rmul__(self, other):\n", "        return self * other"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["X = Matrix([[1, 2, 3],\n", "            [4, 5, 6]])\n", "Y = Matrix([[7, 8, 9],\n", "            [10, 11, 12]])"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ 8  10  12]\n", "[14  16  18]"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["X + Y"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0   4.0   6.0]\n", "[8.0  10.0  12.0]"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["2.0 * X "]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2.0   4.0   6.0]\n", "[8.0  10.0  12.0]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["X * 2.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now comes the hard part: matrix multiplication. For $ X \\in \\mathbb{R}^{n \\times l} $, $ Y \\in \\mathbb{R}^{l \\times m} $, if product is denoted by $ XY = Z \\in \\mathbb{R}^{n \\times m} $, then the general formula for $ z_{i, j} $ is\n", "\n", "$$\n", "z_{i, j} = \\sum_{k=1}^{l} x_{i, k} y_{k, j}.\n", "$$\n", "\n", "In Python, the matrix multiplication is denoted by the arcane `@` operator, implemented by the `__matmul__` magic method. How should we implement this? With *two* list comprehensions this time, instead of one! (Or two `for` loops if you are old-school.)\n", "\n", "Let's jump into the fray."]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["class Matrix(Matrix):\n", "    def row(self, i):\n", "        return self.rows[i]\n", "    \n", "    def col(self, j):\n", "        return [row[j] for row in self.rows]\n", "\n", "    def __matmul__(self, other):\n", "        n, m = self.shape\n", "\n", "        prod_rows = [\n", "            [\n", "                sum([x_ik * y_kj for x_ik, y_kj in zip(self.row(i), other.col(j))]) \n", "                for j in range(m)\n", "            ]\n", "            for i in range(m)\n", "        ]\n", "\n", "        return Matrix(prod_rows)\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's unpack this step by step. First, we added a `row` and `col` helper method that returns the given — surprise! — rows and columns. This is just to avoid cramming one more list comprehension into what's about to be a complicated expression in itself.\n", "\n", "Now, let's tackle the beast that is `__matmul__`. We record the shape of the matrix for convenience, then we start building the double list comprehension that'll describe the product matrix. Whenever we conjure formulas like this, we are not thinking linearly. As we are about to construct the product *row by row*, this is what I wrote down first:\n", "\n", "```<PERSON>\n", "prod_rows = [\n", "    [ ]\n", "    for i in range(m)\n", "]\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we can start to think about what's in the inner ~~loop~~ comprehension. What we know for sure is that it runs through the *columns* of the product:\n", "\n", "```<PERSON>\n", "prod_rows = [\n", "    [\n", "        # to be determined\n", "        for j in range(n)\n", "    ]\n", "    for i in range(m)\n", "]\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What's missing is the expression $ \\sum_{k=1}^{l} x_{i, k} y_{k, j} $, which is — you guessed it — yet another list comprehension, running through the $ i $-th row and $ j $-th column:\n", "\n", "```<PERSON>\n", "sum([x_ik * y_kj for x_ik, y_kj in zip(self.row(i), other.col(j))])\n", "```\n", "\n", "This leaves us with the final formula\n", "\n", "```<PERSON>\n", "prod_rows = [\n", "    [\n", "        sum([x_ik * y_kj for x_ik, y_kj in zip(self.row(i), other.col(j))]) \n", "        for j in range(m)\n", "    ]\n", "    for i in range(m)\n", "]\n", "```\n", "\n", "Let's test this."]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["X = Matrix([[1, 2], [3, 4]])\n", "Y = Matrix([[5, 6], [7, 8]])"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["[19  22]\n", "[43  50]"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["X @ Y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It works. Does this give the correct results? I leave the testing to you, but here's a nice tidbit. Check out the matrix `F` defined below."]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["F = Matrix([[1, 1],\n", "            [1, 0]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What happens if we start calculating $ F^2 $, $ F^3 $, $ F^4 $, and so on?"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2  1]\n", "[1  1]"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["F @ F"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/plain": ["[3  2]\n", "[2  1]"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["F @ F @ F"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["[5  3]\n", "[3  2]"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["F @ F @ F @ F"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/plain": ["[8  5]\n", "[5  3]"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["F @ F @ F @ F @ F"]}, {"cell_type": "markdown", "metadata": {}, "source": ["These are the <PERSON><PERSON><PERSON><PERSON> numbers! You can check this by hand and run a test to verify the correctness of our matrix multiplication. (Or, if you want to be precise, to increase our confidence in the correctness of our matrix multiplication.)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Matrices in NumPy\n", "\n", "One more thing to go. Will we use our custom `Matrix` class for our neural networks? No. We'll use NumPy! Regarding the interface, it's identical to our `Matrix`. Or, to be more precise, the interface of our `Matrix` is identical to NumPy's matrices."]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["X = np.array([[1, 2],\n", "              [3, 4]])\n", "Y = np.array([[5, 6],\n", "              [7, 8]])"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 6,  8],\n", "       [10, 12]])"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["X + Y"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[19, 22],\n", "       [43, 50]])"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["X @ Y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The dimensions of NumPy arrays are described by their `shape` attribute."]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2, 2)"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["X.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are two things I want to mention about NumPy arrays before we move on: *reshaping* and *broadcasting*.\n", "\n", "First, one of the most essential NumPy array operations is *reshaping*, allowing us to restructure matrices."]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["X = np.array([[1, 2, 3],\n", "              [4, 5, 6]])"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1],\n", "       [2],\n", "       [3],\n", "       [4],\n", "       [5],\n", "       [6]])"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["<PERSON><PERSON>reshape(6, 1)"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2, 3],\n", "       [4, 5, 6]])"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["<PERSON><PERSON>reshape(2, 3)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2],\n", "       [3, 4],\n", "       [5, 6]])"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["<PERSON><PERSON>reshape(3, 2)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2, 3, 4, 5, 6]])"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["<PERSON><PERSON>reshape(1, 6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's revisit the array operations. Check out the following."]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["x = np.array([1, 2])\n", "Y = np.array([[1, 2], [3, 4], [5, 6]])"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2, 4],\n", "       [4, 6],\n", "       [6, 8]])"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["x + Y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What happened? `x` and `Y` had a different shape, yet the operation was completed, even with results that make sense! <PERSON><PERSON><PERSON><PERSON> took the (row) vector `x`, copied it below each other to match the dimensions of `Y`, then added these together elementwise. This is called *broadcasting*, which we'll frequently encounter on our journey. Sometimes it'll make our job easier, sometimes it'll make our job harder.\n", "\n", "With all the knowledge about vectors, matrices, and NumPy under our belt, we are ready to tackle *computational graphs*. See you in the next lesson!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Problems"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Problem 1.** Let's revisit the custom `Vector` class. There are a couple of things missing, for instance, *subtraction* and *exponentiation*. Check it out."]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["x = Vector([1, 2, 3])\n", "y = Vector([4, 5, 6])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x - y"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x**2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["These are all supported by NumPy."]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["x_np = np.array([1, 2, 3])\n", "y_np = np.array([4, 5, 6])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_np - y_np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_np ** 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The support for Python's `-` operator can be added via the `__neg__` method, while exponentiation is added via `__pow__`. Implement them!"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["class Vector(Vector):  \n", "    def __neg__(self, other):\n", "        pass\n", "\n", "    def __pow__(self, exp):\n", "        return Vector(coords=[x ** exp for x in self.coords])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Problem 2.** Similarly to **Problem 1**, implement subtraction and exponentiation for our custom `Matrix` class."]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["class Matrix(Matrix):\n", "    def __neg__(self, other):\n", "        pass\n", "\n", "    def __pow__(self, exp):\n", "        pass"]}], "metadata": {"kernelspec": {"display_name": "neural-networks-from-scratch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}